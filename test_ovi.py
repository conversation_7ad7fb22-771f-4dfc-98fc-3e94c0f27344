#!/usr/bin/env python3
"""
Simple test script to verify Ovi setup and run basic inference
"""

import os
import sys
import subprocess

def check_environment():
    """Check if the virtual environment and dependencies are properly set up"""
    print("🔍 Checking Ovi setup...")
    
    # Check if we're in virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment is active")
    else:
        print("❌ Virtual environment not detected")
        return False
    
    # Check key dependencies
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__} installed")
        print(f"   CUDA available: {torch.cuda.is_available()}")
    except ImportError:
        print("❌ PyTorch not installed")
        return False
    
    try:
        import gradio
        print(f"✅ Gradio {gradio.__version__} installed")
    except ImportError:
        print("❌ Gradio not installed")
        return False
    
    # Check if model weights exist
    ckpts_dir = "./ckpts"
    if os.path.exists(ckpts_dir):
        print(f"✅ Checkpoints directory exists: {ckpts_dir}")
        
        # Check for key model files
        wan_dir = os.path.join(ckpts_dir, "Wan2.2-TI2V-5B")
        mmaudio_dir = os.path.join(ckpts_dir, "MMAudio")
        ovi_dir = os.path.join(ckpts_dir, "Ovi")
        
        if os.path.exists(wan_dir):
            print(f"✅ Wan2.2 model directory found")
        else:
            print(f"⏳ Wan2.2 model directory not found (may still be downloading)")
            
        if os.path.exists(mmaudio_dir):
            print(f"✅ MMAudio model directory found")
        else:
            print(f"⏳ MMAudio model directory not found (may still be downloading)")
            
        if os.path.exists(ovi_dir):
            print(f"✅ Ovi model directory found")
        else:
            print(f"⏳ Ovi model directory not found (may still be downloading)")
    else:
        print(f"❌ Checkpoints directory not found: {ckpts_dir}")
        return False
    
    return True

def run_gradio_app():
    """Run the Gradio application"""
    print("\n🚀 Starting Ovi Gradio application...")
    print("Note: This will start a web interface. Use Ctrl+C to stop.")
    print("The interface will be available at: http://localhost:7860")
    
    try:
        # Run the gradio app with CPU offload for lower memory usage
        cmd = [sys.executable, "Ovi/gradio_app.py", "--cpu_offload"]
        subprocess.run(cmd, cwd=".")
    except KeyboardInterrupt:
        print("\n👋 Gradio application stopped by user")
    except Exception as e:
        print(f"❌ Error running Gradio app: {e}")

def main():
    """Main function"""
    print("🎬 Ovi Setup Test Script")
    print("=" * 50)
    
    if not check_environment():
        print("\n❌ Setup incomplete. Please ensure all dependencies are installed and models are downloaded.")
        return
    
    print("\n✅ Setup looks good!")
    
    # Ask user if they want to run the Gradio app
    response = input("\nWould you like to start the Gradio web interface? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        run_gradio_app()
    else:
        print("\n📝 To run Ovi later, use:")
        print("   python Ovi/gradio_app.py --cpu_offload")
        print("\n📖 For more options, check the README.md file")

if __name__ == "__main__":
    main()
